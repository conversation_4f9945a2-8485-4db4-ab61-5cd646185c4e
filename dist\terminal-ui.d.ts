import { MasterAI } from './master-ai';
import { Logger } from './logger';
export declare class TerminalUI {
    private screen;
    private masterAI;
    private logger;
    private state;
    private boxes;
    constructor(masterAI: MasterAI, logger: Logger);
    private initializeScreen;
    private setupLayout;
    private setupEventHandlers;
    private handleUserInput;
    private addToChatPanel;
    private updateWorkerStatus;
    private updateLogPanel;
    private showHelp;
    start(): Promise<void>;
}
//# sourceMappingURL=terminal-ui.d.ts.map