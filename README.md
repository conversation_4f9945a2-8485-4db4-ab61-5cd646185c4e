# Terminal AI Orchestrator (TAO)

Terminal içerisinde yaşayan, çoklu AI modeli orkestrasyon sistemi. Master-Worker mimar<PERSON> ile çalışır ve OpenAI uyumlu providerları destekler.

## Özellikler

- **Master AI**: Diğer AI modellerini yöneten ana kontrol birimi
- **Worker AI'lar**: Özelleşmiş görevleri yerine getiren yardımcı modeller
- **Real-time İletişim**: AI modelleri arası sürekli bilgi akışı
- **OpenAI Uyumlu**: Herhangi bir OpenAI compatible provider kullanabilir
- **Konfigürasyon**: Kullanıcı tanımlı model ayarları
- **Terminal UI**: Interaktif terminal arayüzü

## Mi<PERSON>i

```
┌─────────────────┐
│   Master AI     │ ←→ User Interface
│   (Orchestrator)│
└─────────┬───────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───┐
│Worker │   │Worker │
│AI #1  │ ←→│AI #2  │
└───────┘   └───────┘
```

## <PERSON><PERSON><PERSON>

```bash
npm install
npm run build
npm start
```

## Konfigürasyon

`config/models.json` dosyasını düzenleyerek AI modellerinizi yapılandırın.