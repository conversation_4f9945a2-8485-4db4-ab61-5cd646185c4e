{"version": 3, "file": "terminal-ui.js", "sourceRoot": "", "sources": ["../src/terminal-ui.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8B;AAK9B,MAAa,UAAU;IAOrB,YAAY,QAAkB,EAAE,MAAc;QAFtC,UAAK,GAA2B,EAAE,CAAC;QAGzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG;YACX,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,iBAAO,CAAC,MAAM,CAAC;YAC3B,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,0BAA0B;SAClC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE;YAC3C,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW;QACjB,gBAAgB;QAChB,MAAM,SAAS,GAAG,iBAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO;aACZ;SACF,CAAC,CAAC;QAEH,SAAS;QACT,MAAM,MAAM,GAAG,iBAAO,CAAC,GAAG,CAAC;YACzB,MAAM,EAAE,SAAS;YACjB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,8CAA8C;YACvD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM;gBACV,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,SAAS,GAAG,iBAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,kBAAkB;YACzB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAEjC,4BAA4B;QAC5B,MAAM,SAAS,GAAG,iBAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,UAAU;YACjB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,OAAO;iBACZ;aACF;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAEjC,qBAAqB;QACrB,MAAM,QAAQ,GAAG,iBAAO,CAAC,GAAG,CAAC;YAC3B,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,UAAU;YACjB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,QAAQ;iBACb;aACF;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE/B,0BAA0B;QAC1B,MAAM,UAAU,GAAG,iBAAO,CAAC,OAAO,CAAC;YACjC,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,yCAAyC;YAChD,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,OAAO;iBACZ;aACF;YACD,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QAEnC,eAAe;QACf,MAAM,SAAS,GAAG,iBAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,6CAA6C;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM;gBACV,EAAE,EAAE,OAAO;aACZ;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;IACnC,CAAC;IAEO,kBAAkB;QACxB,iBAAiB;QACjB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAa,EAAE,EAAE;YACzD,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACxC,IAAI,CAAC,KAAK,CAAC,UAAkB,CAAC,UAAU,EAAE,CAAC;gBAC5C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;YAC7C,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAAa;QACzC,wCAAwC;QACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC;QAE7C,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,cAAc,CAAC,cAAc,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;YAEvD,8BAA8B;YAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,QAAgB,OAAO;QAC7D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,KAAK,QAAQ,SAAS,KAAK,OAAO,KAAK,CAAC;QAErE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QACzD,MAAM,UAAU,GAAG,cAAc,GAAG,IAAI,GAAG,gBAAgB,CAAC;QAE5D,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC;IACxE,CAAC;IAEO,kBAAkB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;YACrF,MAAM,IAAI,GAAG,SAAS,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC;YAC5C,MAAM,IAAI,GAAG,aAAa,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC;YAE3D,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC;YAC/B,OAAO,IAAI,UAAU,MAAM,IAAI,CAAC;YAChC,OAAO,IAAI,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QACnD,OAAO,IAAI,4BAA4B,WAAW,CAAC,MAAM,IAAI,CAAC;QAE9D,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC/D,OAAO,IAAI,IAAI,WAAW,SAAS,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAEO,cAAc;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO;gBAAE,OAAO;YAE3D,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAChC,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;oBACjC,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;YAE3D,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAChD,OAAO,IAAI,IAAI,UAAU,QAAQ,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC;YACzE,OAAO,IAAI,YAAY,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,OAAO,SAAS,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;IACtE,CAAC;IAEO,QAAQ;QACd,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;KAmBhB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAE9B,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAErB,6BAA6B;QAC7B,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;CACF;AAnTD,gCAmTC"}