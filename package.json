{"name": "terminal-ai-orchestrator", "version": "1.0.0", "description": "Terminal tabanlı çoklu AI modeli orkestrasyon sistemi", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js start", "dev": "ts-node src/index.ts start", "watch": "tsc -w", "config": "node dist/index.js config"}, "keywords": ["ai", "terminal", "orchestrator", "openai"], "author": "inkbytefo", "license": "MIT", "dependencies": {"axios": "^1.6.0", "blessed": "^0.1.81", "chalk": "^4.1.2", "commander": "^11.0.0", "inquirer": "^9.2.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/blessed": "^0.1.21", "@types/inquirer": "^9.0.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}