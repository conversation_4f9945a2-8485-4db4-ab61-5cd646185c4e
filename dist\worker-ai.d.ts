import { Message, Task, AIModel } from './types';
import { Logger } from './logger';
export declare class WorkerAI {
    private aiService;
    private model;
    private masterAI;
    private logger;
    private messageHistory;
    private currentTask?;
    constructor(model: AIModel, masterAI: any, logger: Logger);
    assignTask(task: Task): Promise<void>;
    receiveMessage(content: string, from: string): Promise<string>;
    private buildTaskContext;
    getId(): string;
    getName(): string;
    getSpecialization(): string;
    getCapabilities(): string[];
    getCurrentTask(): Task | undefined;
    getMessageHistory(): Message[];
    isAvailable(): boolean;
    getStatus(): string;
}
//# sourceMappingURL=worker-ai.d.ts.map