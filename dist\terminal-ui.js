"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalUI = void 0;
const blessed_1 = __importDefault(require("blessed"));
class TerminalUI {
    constructor(masterAI, logger) {
        this.boxes = {};
        this.masterAI = masterAI;
        this.logger = logger;
        this.state = {
            currentView: 'main',
            showDebug: false
        };
        this.initializeScreen();
        this.setupLayout();
        this.setupEventHandlers();
    }
    initializeScreen() {
        this.screen = blessed_1.default.screen({
            smartCSR: true,
            title: 'Terminal AI Orchestrator'
        });
        // ESC tuşu ile çıkış
        this.screen.key(['escape', 'q', 'C-c'], () => {
            return process.exit(0);
        });
    }
    setupLayout() {
        // Ana container
        const container = blessed_1.default.box({
            parent: this.screen,
            width: '100%',
            height: '100%',
            style: {
                bg: 'black'
            }
        });
        // Başlık
        const header = blessed_1.default.box({
            parent: container,
            top: 0,
            left: 0,
            width: '100%',
            height: 3,
            content: '{center}🤖 Terminal AI Orchestrator{/center}',
            tags: true,
            style: {
                bg: 'blue',
                fg: 'white',
                bold: true
            }
        });
        // Sol panel - Worker durumları
        const leftPanel = blessed_1.default.box({
            parent: container,
            label: ' Worker AI\'lar ',
            top: 3,
            left: 0,
            width: '30%',
            height: '70%',
            border: {
                type: 'line'
            },
            style: {
                border: {
                    fg: 'cyan'
                }
            },
            scrollable: true,
            alwaysScroll: true
        });
        this.boxes.leftPanel = leftPanel;
        // Orta panel - Sohbet alanı
        const chatPanel = blessed_1.default.box({
            parent: container,
            label: ' Sohbet ',
            top: 3,
            left: '30%',
            width: '50%',
            height: '70%',
            border: {
                type: 'line'
            },
            style: {
                border: {
                    fg: 'green'
                }
            },
            scrollable: true,
            alwaysScroll: true
        });
        this.boxes.chatPanel = chatPanel;
        // Sağ panel - Loglar
        const logPanel = blessed_1.default.box({
            parent: container,
            label: ' Loglar ',
            top: 3,
            left: '80%',
            width: '20%',
            height: '70%',
            border: {
                type: 'line'
            },
            style: {
                border: {
                    fg: 'yellow'
                }
            },
            scrollable: true,
            alwaysScroll: true
        });
        this.boxes.logPanel = logPanel;
        // Alt panel - Giriş alanı
        const inputPanel = blessed_1.default.textbox({
            parent: container,
            label: ' Mesajınız (Enter: Gönder, Esc: Çıkış) ',
            bottom: 0,
            left: 0,
            width: '100%',
            height: 3,
            border: {
                type: 'line'
            },
            style: {
                border: {
                    fg: 'white'
                }
            },
            inputOnFocus: true
        });
        this.boxes.inputPanel = inputPanel;
        // Durum çubuğu
        const statusBar = blessed_1.default.box({
            parent: container,
            bottom: 3,
            left: 0,
            width: '100%',
            height: 1,
            content: 'Hazır | F1: Yardım | F2: Durum | F3: Debug | F4: Performans | F5: İstatistik | ESC: Çıkış',
            style: {
                bg: 'gray',
                fg: 'black'
            }
        });
        this.boxes.statusBar = statusBar;
    }
    setupEventHandlers() {
        // Input handling
        this.boxes.inputPanel.on('submit', async (value) => {
            if (value.trim()) {
                await this.handleUserInput(value.trim());
                this.boxes.inputPanel.clearValue();
                this.boxes.inputPanel.focus();
            }
        });
        // Klavye kısayolları
        this.screen.key(['f1'], () => {
            this.showHelp();
        });
        this.screen.key(['f2'], () => {
            this.updateWorkerStatus();
        });
        this.screen.key(['f3'], () => {
            this.state.showDebug = !this.state.showDebug;
            this.updateLogPanel();
        });
        this.screen.key(['f4'], () => {
            this.showPerformanceReport();
        });
        this.screen.key(['f5'], () => {
            this.showSystemStatistics();
        });
    }
    async handleUserInput(input) {
        // Kullanıcı mesajını chat paneline ekle
        this.addToChatPanel(`Siz: ${input}`, 'cyan');
        try {
            // Master AI'dan yanıt al
            const response = await this.masterAI.processUserInput(input);
            this.addToChatPanel(`Master AI: ${response}`, 'green');
            // Worker durumlarını güncelle
            this.updateWorkerStatus();
        }
        catch (error) {
            this.addToChatPanel(`Hata: ${error}`, 'red');
        }
        this.screen.render();
    }
    addToChatPanel(message, color = 'white') {
        const timestamp = new Date().toLocaleTimeString();
        const formattedMessage = `{${color}-fg}[${timestamp}] ${message}{/}`;
        const currentContent = this.boxes.chatPanel.getContent();
        const newContent = currentContent + '\n' + formattedMessage;
        this.boxes.chatPanel.setContent(newContent);
        this.boxes.chatPanel.scrollTo(this.boxes.chatPanel.getScrollHeight());
    }
    updateWorkerStatus() {
        const workers = this.masterAI.getWorkers();
        const systemStats = this.masterAI.getSystemStatistics();
        let content = '';
        // System overview
        content += `{bold}{cyan-fg}=== SYSTEM OVERVIEW ==={/}\n`;
        content += `Active Workers: ${systemStats.performance.activeWorkers}\n`;
        content += `Avg Response: ${systemStats.performance.averageSystemResponseTime.toFixed(0)}ms\n`;
        content += `Throughput: ${systemStats.performance.taskThroughput.toFixed(1)}/h\n`;
        content += `Error Rate: ${(systemStats.performance.errorRate * 100).toFixed(1)}%\n\n`;
        // Worker details
        content += `{bold}{green-fg}=== WORKERS ==={/}\n`;
        workers.forEach(worker => {
            const status = worker.isAvailable() ? '{green-fg}Müsait{/}' : '{yellow-fg}Meşgul{/}';
            const name = `{bold}${worker.getName()}{/}`;
            const spec = `{gray-fg}(${worker.getSpecialization()}){/}`;
            content += `${name} ${spec}\n`;
            content += `Durum: ${status}\n`;
            content += `${worker.getStatus()}\n\n`;
        });
        // Active tasks
        const activeTasks = this.masterAI.getActiveTasks();
        content += `{bold}{yellow-fg}=== TASKS ==={/}\n`;
        content += `Aktif: ${activeTasks.length} | Toplam: ${systemStats.taskOrchestrator.taskHistory}\n`;
        activeTasks.slice(0, 3).forEach(task => {
            const statusColor = task.status === 'completed' ? 'green' :
                task.status === 'failed' ? 'red' : 'yellow';
            const priority = task.priority ? `[${task.priority.toUpperCase()}]` : '';
            content += `{${statusColor}-fg}• ${priority} ${task.description.substring(0, 25)}...{/}\n`;
        });
        if (activeTasks.length > 3) {
            content += `{gray-fg}... ve ${activeTasks.length - 3} tane daha{/}\n`;
        }
        this.boxes.leftPanel.setContent(content);
    }
    updateLogPanel() {
        const logs = this.logger.getLogs(undefined, undefined, 20);
        let content = '';
        logs.forEach(log => {
            if (!this.state.showDebug && log.level === 'debug')
                return;
            const levelColor = log.level === 'error' ? 'red' :
                log.level === 'warn' ? 'yellow' :
                    log.level === 'debug' ? 'gray' : 'white';
            const time = log.timestamp.toLocaleTimeString();
            content += `{${levelColor}-fg}[${time}] ${log.level.toUpperCase()}{/}\n`;
            content += `{gray-fg}${log.source}: ${log.message}{/}\n\n`;
        });
        this.boxes.logPanel.setContent(content);
        this.boxes.logPanel.scrollTo(this.boxes.logPanel.getScrollHeight());
    }
    showHelp() {
        const helpText = `
{bold}Terminal AI Orchestrator Yardım{/}

{yellow-fg}Klavye Kısayolları:{/}
F1 - Bu yardım mesajını göster
F2 - Worker durumlarını güncelle
F3 - Debug loglarını aç/kapat
F4 - Performans raporunu göster
F5 - Sistem istatistiklerini göster
ESC - Uygulamadan çık

{yellow-fg}Komutlar:{/}
Herhangi bir mesaj yazıp Enter'a basın
Master AI mesajınızı işleyecek ve uygun
worker AI'lara görevleri dağıtacak

{yellow-fg}Yeni Özellikler:{/}
- Akıllı görev dağıtımı
- Performans izleme
- Bağlam yönetimi
- AI'lar arası iletişim

{yellow-fg}Paneller:{/}
Sol: Worker AI durumları ve sistem özeti
Orta: Sohbet geçmişi
Sağ: Sistem logları
Alt: Mesaj giriş alanı
    `;
        this.addToChatPanel(helpText, 'cyan');
        this.screen.render();
    }
    showPerformanceReport() {
        try {
            const report = this.masterAI.getPerformanceReport();
            let reportText = `
{bold}{green-fg}=== PERFORMANS RAPORU ==={/}

{yellow-fg}Sistem Performansı:{/}
Grade: {bold}${report.systemMetrics.grade}{/}
Ortalama Yanıt Süresi: ${report.systemMetrics.averageSystemResponseTime.toFixed(0)}ms
Hata Oranı: ${(report.systemMetrics.errorRate * 100).toFixed(1)}%
Kaynak Kullanımı: ${(report.systemMetrics.resourceUtilization * 100).toFixed(1)}%
Kullanıcı Memnuniyeti: ${report.systemMetrics.userSatisfactionScore.toFixed(1)}/5

{yellow-fg}Worker Performansları:{/}`;
            report.workerMetrics.forEach((worker) => {
                reportText += `
{bold}${worker.workerName}{/} (Grade: {bold}${worker.grade}{/})
  Başarı Oranı: ${(worker.successRate * 100).toFixed(1)}%
  Ortalama Süre: ${worker.averageResponseTime.toFixed(0)}ms
  Verimlilik: ${worker.efficiency.toFixed(1)} görev/dk
  Kalite: ${worker.qualityScore.toFixed(1)}/5`;
            });
            if (report.recommendations.length > 0) {
                reportText += `

{yellow-fg}Öneriler:{/}`;
                report.recommendations.forEach((rec) => {
                    reportText += `\n• ${rec}`;
                });
            }
            this.addToChatPanel(reportText, 'green');
            this.screen.render();
        }
        catch (error) {
            this.addToChatPanel(`Performans raporu alınamadı: ${error}`, 'red');
        }
    }
    showSystemStatistics() {
        try {
            const stats = this.masterAI.getSystemStatistics();
            let statsText = `
{bold}{blue-fg}=== SİSTEM İSTATİSTİKLERİ ==={/}

{yellow-fg}Görev Orkestratörü:{/}
Toplam Aktif Görevler: ${stats.taskOrchestrator.totalActiveTasks}
Görev Geçmişi: ${stats.taskOrchestrator.taskHistory}

{yellow-fg}Bağlam Yönetimi:{/}
Oturum ID: ${stats.context.sessionId}
Mesaj Sayısı: ${stats.context.totalMessages}
Aktif Konular: ${stats.context.currentTopics.join(', ') || 'Yok'}
Özet Sayısı: ${stats.context.summaryCount}

{yellow-fg}İletişim Protokolü:{/}
Aktif Kuyruklar: ${stats.communication.activeQueues}
Bekleyen Mesajlar: ${stats.communication.totalQueuedMessages}
Aktif İşbirlikleri: ${stats.communication.activeCollaborations}
Bilgi Tabanı: ${stats.communication.knowledgeBaseSize}

{yellow-fg}Sistem Bilgileri:{/}
Çalışma Süresi: ${(stats.performance.systemUptime / (1000 * 60 * 60)).toFixed(1)} saat
Toplam İstek: ${stats.performance.totalRequests}
Saatlik Verim: ${stats.performance.taskThroughput.toFixed(1)} görev/saat`;
            this.addToChatPanel(statsText, 'blue');
            this.screen.render();
        }
        catch (error) {
            this.addToChatPanel(`Sistem istatistikleri alınamadı: ${error}`, 'red');
        }
    }
    async start() {
        // İlk durumu güncelle
        this.updateWorkerStatus();
        this.updateLogPanel();
        // Input alanına odaklan
        this.boxes.inputPanel.focus();
        // Ekranı render et
        this.screen.render();
        // Log güncellemelerini dinle
        setInterval(() => {
            this.updateLogPanel();
            this.screen.render();
        }, 2000);
    }
}
exports.TerminalUI = TerminalUI;
//# sourceMappingURL=terminal-ui.js.map