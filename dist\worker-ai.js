"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerAI = void 0;
const uuid_1 = require("uuid");
const ai_service_1 = require("./ai-service");
class WorkerAI {
    constructor(model, masterAI, logger) {
        this.messageHistory = [];
        this.model = model;
        this.aiService = new ai_service_1.AIService(model);
        this.masterAI = masterAI;
        this.logger = logger;
        this.logger.info('WorkerAI', `Worker AI başlatıldı: ${model.name} (${model.specialization})`);
    }
    async assignTask(task) {
        this.currentTask = task;
        task.status = 'in_progress';
        this.logger.info('WorkerAI', `Görev atandı: ${task.id} - ${task.description}`);
        try {
            const taskMessage = {
                id: (0, uuid_1.v4)(),
                from: 'master',
                to: this.model.id,
                content: `Görev: ${task.description}`,
                timestamp: new Date(),
                type: 'system'
            };
            this.messageHistory.push(taskMessage);
            const response = await this.aiService.sendMessage(this.messageHistory, this.buildTaskContext(task));
            const result = response.content;
            // Sonucu master AI'a gönder
            await this.masterAI.receiveWorkerResponse(this.model.id, task.id, result);
            this.currentTask = undefined;
            this.logger.info('WorkerAI', `Görev tamamlandı: ${task.id}`);
        }
        catch (error) {
            task.status = 'failed';
            this.logger.error('WorkerAI', `Görev başarısız: ${task.id} - ${error}`);
            throw error;
        }
    }
    async receiveMessage(content, from) {
        const message = {
            id: (0, uuid_1.v4)(),
            from,
            to: this.model.id,
            content,
            timestamp: new Date(),
            type: 'inter_ai'
        };
        this.messageHistory.push(message);
        try {
            const response = await this.aiService.sendMessage(this.messageHistory);
            const responseMessage = {
                id: (0, uuid_1.v4)(),
                from: this.model.id,
                to: from,
                content: response.content,
                timestamp: new Date(),
                type: 'ai_response'
            };
            this.messageHistory.push(responseMessage);
            return response.content;
        }
        catch (error) {
            this.logger.error('WorkerAI', `Mesaj işleme hatası: ${error}`);
            return `Hata: ${error}`;
        }
    }
    buildTaskContext(task) {
        return `
Görev ID: ${task.id}
Görev Açıklaması: ${task.description}
Uzmanlık Alanım: ${this.model.specialization}
Yeteneklerim: ${this.model.capabilities.join(', ')}
Görev Durumu: ${task.status}
Oluşturulma Zamanı: ${task.createdAt.toISOString()}

Bu görevi yeteneklerim dahilinde en iyi şekilde tamamlamalıyım.
    `.trim();
    }
    getId() {
        return this.model.id;
    }
    getName() {
        return this.model.name;
    }
    getSpecialization() {
        return this.model.specialization || 'general';
    }
    getCapabilities() {
        return this.model.capabilities;
    }
    getCurrentTask() {
        return this.currentTask;
    }
    getMessageHistory() {
        return this.messageHistory;
    }
    isAvailable() {
        return !this.currentTask || this.currentTask.status === 'completed';
    }
    getStatus() {
        if (this.currentTask) {
            return `Görev: ${this.currentTask.description} (${this.currentTask.status})`;
        }
        return 'Beklemede';
    }
}
exports.WorkerAI = WorkerAI;
//# sourceMappingURL=worker-ai.js.map