"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasterAI = void 0;
const uuid_1 = require("uuid");
const ai_service_1 = require("./ai-service");
const worker_ai_1 = require("./worker-ai");
class MasterAI {
    constructor(model, workers, logger) {
        this.workers = new Map();
        this.activeTasks = new Map();
        this.aiService = new ai_service_1.AIService(model);
        this.logger = logger;
        this.context = {
            messages: [],
            participants: [model.name]
        };
        // Worker AI'ları başlat
        workers.forEach(workerModel => {
            const worker = new worker_ai_1.WorkerAI(workerModel, this, logger);
            this.workers.set(workerModel.id, worker);
            this.context.participants.push(workerModel.name);
        });
        this.logger.info('MasterAI', `Master AI başlatıldı. ${workers.length} worker AI aktif.`);
    }
    async processUserInput(input) {
        const userMessage = {
            id: (0, uuid_1.v4)(),
            from: 'user',
            to: 'master',
            content: input,
            timestamp: new Date(),
            type: 'user'
        };
        this.context.messages.push(userMessage);
        this.logger.debug('MasterAI', `Kullanıcı mesajı alındı: ${input.substring(0, 50)}...`);
        try {
            // Master AI'dan yanıt al
            const response = await this.aiService.sendMessage(this.context.messages, this.buildContextString());
            const masterResponse = {
                id: (0, uuid_1.v4)(),
                from: 'master',
                to: 'user',
                content: response.content,
                timestamp: new Date(),
                type: 'ai_response'
            };
            this.context.messages.push(masterResponse);
            // Görev dağıtımı gerekli mi kontrol et
            await this.analyzeAndDistributeTasks(input, response.content);
            return response.content;
        }
        catch (error) {
            this.logger.error('MasterAI', `Hata: ${error}`);
            return `Üzgünüm, bir hata oluştu: ${error}`;
        }
    }
    async analyzeAndDistributeTasks(userInput, masterResponse) {
        // Basit görev analizi - gerçek uygulamada daha sofistike olabilir
        const needsCodeAnalysis = /kod|code|debug|hata|error|function|class/i.test(userInput);
        const needsResearch = /araştır|research|bilgi|information|nedir|what is/i.test(userInput);
        if (needsCodeAnalysis) {
            const codeWorker = Array.from(this.workers.values())
                .find(w => w.getSpecialization() === 'code_analysis');
            if (codeWorker) {
                const task = await this.createTask(`Kod analizi: ${userInput}`, codeWorker.getId());
                await codeWorker.assignTask(task);
            }
        }
        if (needsResearch) {
            const researchWorker = Array.from(this.workers.values())
                .find(w => w.getSpecialization() === 'research');
            if (researchWorker) {
                const task = await this.createTask(`Araştırma: ${userInput}`, researchWorker.getId());
                await researchWorker.assignTask(task);
            }
        }
    }
    async createTask(description, assignedTo) {
        const task = {
            id: (0, uuid_1.v4)(),
            description,
            assignedTo,
            status: 'pending',
            createdAt: new Date()
        };
        this.activeTasks.set(task.id, task);
        this.logger.info('MasterAI', `Yeni görev oluşturuldu: ${task.id}`);
        return task;
    }
    async receiveWorkerResponse(workerId, taskId, result) {
        const task = this.activeTasks.get(taskId);
        if (task) {
            task.status = 'completed';
            task.result = result;
            task.completedAt = new Date();
            this.logger.info('MasterAI', `Görev tamamlandı: ${taskId} by ${workerId}`);
            // Worker sonucunu context'e ekle
            const workerMessage = {
                id: (0, uuid_1.v4)(),
                from: workerId,
                to: 'master',
                content: `Görev Sonucu (${taskId}): ${result}`,
                timestamp: new Date(),
                type: 'inter_ai'
            };
            this.context.messages.push(workerMessage);
        }
    }
    buildContextString() {
        const activeWorkers = Array.from(this.workers.values())
            .map(w => `${w.getName()} (${w.getSpecialization()})`)
            .join(', ');
        const activeTasks = Array.from(this.activeTasks.values())
            .filter(t => t.status !== 'completed')
            .map(t => `${t.id}: ${t.description} (${t.status})`)
            .join('\n');
        return `
Aktif Worker AI'lar: ${activeWorkers}
Aktif Görevler: 
${activeTasks || 'Yok'}
Son Mesajlar: ${this.context.messages.slice(-3).map(m => `${m.from}: ${m.content.substring(0, 100)}`).join('\n')}
    `.trim();
    }
    getWorkers() {
        return Array.from(this.workers.values());
    }
    getActiveTasks() {
        return Array.from(this.activeTasks.values());
    }
    getContext() {
        return this.context;
    }
    async broadcastToWorkers(message) {
        const promises = Array.from(this.workers.values()).map(worker => worker.receiveMessage(message, 'master'));
        await Promise.all(promises);
        this.logger.info('MasterAI', 'Mesaj tüm worker AI\'lara gönderildi');
    }
}
exports.MasterAI = MasterAI;
//# sourceMappingURL=master-ai.js.map