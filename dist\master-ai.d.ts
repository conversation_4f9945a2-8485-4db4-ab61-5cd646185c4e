import { <PERSON><PERSON><PERSON> } from './worker-ai';
import { Task, AIModel, ConversationContext } from './types';
import { Logger } from './logger';
export declare class MasterAI {
    private aiService;
    private workers;
    private context;
    private logger;
    private activeTasks;
    constructor(model: AIModel, workers: AIModel[], logger: Logger);
    processUserInput(input: string): Promise<string>;
    private analyzeAndDistributeTasks;
    private createTask;
    receiveWorkerResponse(workerId: string, taskId: string, result: string): Promise<void>;
    private buildContextString;
    getWorkers(): WorkerAI[];
    getActiveTasks(): Task[];
    getContext(): ConversationContext;
    broadcastToWorkers(message: string): Promise<void>;
}
//# sourceMappingURL=master-ai.d.ts.map