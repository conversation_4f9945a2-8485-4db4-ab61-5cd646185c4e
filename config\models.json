{"master": {"name": "Master Orchestrator", "role": "master", "provider": {"baseURL": "https://api.openai.com/v1", "apiKey": "test-key-replace-with-real", "model": "gpt-4"}, "systemPrompt": "Sen bir Master AI'sın. Diğer AI modellerini yönetir, görevleri dağıtır ve sonuçları koordine edersin. Kullanıcı isteklerini analiz edip uygun worker AI'lara yönlendir<PERSON>in.", "capabilities": ["orchestration", "task_distribution", "result_coordination"], "maxTokens": 4000, "temperature": 0.7}, "workers": [{"id": "worker_1", "name": "Code Specialist", "role": "worker", "specialization": "code_analysis", "provider": {"baseURL": "https://api.openai.com/v1", "apiKey": "your-api-key-here", "model": "gpt-3.5-turbo"}, "systemPrompt": "Sen bir kod uzmanısın. Kod analizi, debugging, optimizasyon ve kod yazma konularında uzmanlaşmışsın. Master AI'dan gelen görevleri yerine getirirsin.", "capabilities": ["code_analysis", "debugging", "optimization", "code_generation"], "maxTokens": 2000, "temperature": 0.3}, {"id": "worker_2", "name": "Research Assistant", "role": "worker", "specialization": "research", "provider": {"baseURL": "https://api.openai.com/v1", "apiKey": "your-api-key-here", "model": "gpt-3.5-turbo"}, "systemPrompt": "Sen bir araştı<PERSON> u<PERSON>. <PERSON><PERSON><PERSON>, analiz etme ve raporlama konularında uzmanlaşmışsın. Master AI'dan gelen araştırma görevlerini yerine getiri<PERSON>in.", "capabilities": ["research", "analysis", "documentation", "fact_checking"], "maxTokens": 2000, "temperature": 0.5}], "communication": {"enableInterWorkerCommunication": true, "messageTimeout": 30000, "maxRetries": 3}}